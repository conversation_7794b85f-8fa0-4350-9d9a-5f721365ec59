const axios = require('axios');
const { HttpsProxyAgent } = require('https-proxy-agent');
const crypto = require('./extracted_crypto_function.js');
const zlib = require('zlib');
const fs = require('fs');

process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const CONFIG = {
    proxy: { host: '127.0.0.1', port: 2024 },
    baseUrl: "https://sdk.mini.stargame.group/api",
    token: "eyJhbGciOiJIUzI1NiJ9.eyJnYW1lSWQiOjEwLCJzaWduSW5UeXBlIjoiVVNFUiIsImdhbWVDaGFubmVsTWFzdGVyQ29kZU5vIjoiV0VDSEFUIiwic2Vzc2lvbktleSI6IlBBbTVwOW14Uzg0Y0N6VGRuVlBOVEE9PSIsImdhbWVDaGFubmVsTWFzdGVySWQiOjE2MSwiZ2FtZVVzZXJJZCI6MTk1NzY5MTE4NDY4NDA3NzA1NywiZ2FtZVVzZXJFeHRlcm5hbElkIjoibzZwaVg2Njh2eEVFQW1PRjZPM2FuNEszQ052ZyIsImdhbWVDaGFubmVsSWQiOjE3fQ.vUbIRP1bOWbzUtQgkYGhlB-fsTb9bWNvWgxhMkDY8cM",
    headers: {
        'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'Keep-Alive', 'Content-Type': 'application/json; Charset=UTF-8',
        'Host': 'sdk.mini.stargame.group', 'Referer': 'https://servicewechat.com/wx3f2ac2c06e796c9b/78/page-frame.html',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) UnifiedPCWindowsWechat(0xf2540517) XWEB/13909',
        'xweb_xhr': '1'
    }
};

async function makeRequest({ method = 'GET', url, data, authorization, headers = {}, timeout = 30000 }) {
    try {
        const { proxy } = CONFIG;
        const proxyUrl = proxy.username ?
            `http://${proxy.username}:${proxy.password}@${proxy.host}:${proxy.port}` :
            `http://${proxy.host}:${proxy.port}`;

        const config = {
            method: method.toUpperCase(), url, timeout,
            headers: { ...CONFIG.headers, ...headers, ...(authorization && { Authorization: authorization }) },
            httpsAgent: new HttpsProxyAgent(proxyUrl, { rejectUnauthorized: false }),
            ...(data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) && { data })
        };

        console.log(`${method.toUpperCase()} ${url}`);
        const response = await axios(config);
        console.log(`✓ ${response.status}`);
        return response.data;

    } catch (error) {
        console.error(`✗ ${error.message}`);
        if (error.response) {
            throw new Error(`请求失败: ${error.response.status} - ${JSON.stringify(error.response.data)}`);
        }
        throw error;
    }
}

// 获取存档
async function getArchive() {
    const url = CONFIG.baseUrl + "/game_user/get_game_archive";
    const response = await makeRequest({
        url,
        authorization: `Bearer ${CONFIG.token}`
    });

    if (!response || !response.succeed) {
        throw new Error("获取存档失败: " + JSON.stringify(response));
    }

    const archiveHex = response.data.archive.replace(/\n/g, "");
    const binaryData = Buffer.from(archiveHex, 'hex');
    const decoded = zlib.gunzipSync(binaryData).toString();
    let data = JSON.parse(decoded);
    console.log(data)

    return {
        data: data,
        version: parseInt(response.data.version)
    };
}

// 修改并上传存档
async function updateArchive(data, version) {
    const url = CONFIG.baseUrl + "/game_user/sync_game_archive";

    // 修改金币为 999999
    data['22'] = version ;
    data['16'] = '哈哈哈哈';
    const key = data["12"].toString(); // 使用用户UID作为加密密钥

    const result = crypto.signData(data, key);
    // 判断 result 类型

    fs.writeFileSync("data.json", JSON.stringify(result), "utf-8");

    console.log("\n处理后的关键字段:");
    console.log("- 时间戳:", result['_time_']);
    console.log("- 签名:", result["-1"]);

    // 压缩 + 转 HEX
    const jsonStr = JSON.stringify(result);
    const gzData = zlib.gzipSync(jsonStr);
    const archiveHex = gzData.toString('hex');

    const payload = {
        "version": version +1 ,
        "archive": archiveHex
    };

    const response = await makeRequest({
        method: 'POST',
        url,
        data: payload,
        authorization: `Bearer ${CONFIG.token}`
    });

    console.log("上传结果:", response);
    return response;
}

// 主函数
async function main() {
    try {
        console.log("开始获取存档...");
        const archiveInfo = await getArchive();

        // 删除时间相关字段
        delete archiveInfo.data["_time_"];
        delete archiveInfo.data["timestamp"];
        delete archiveInfo.data["-1"];

        console.log("存档获取成功，开始修改并上传...");
        await updateArchive(archiveInfo.data, archiveInfo.version);

        console.log("操作完成！");
    } catch (error) {
        console.error("主程序执行失败:", error.message);
    }
}

// 执行主函数
main();